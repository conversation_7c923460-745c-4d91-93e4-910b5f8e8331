<template>
  <div class="aggregation-page">
    <!-- 顶部标签页导航 -->
    <div class="tab-navigation">
      <div class="tab-item active">
        <span class="tab-text">多任务菜单</span>
        <i class="el-icon-close tab-close"></i>
      </div>
      <div class="tab-item">
        <span class="tab-text">多任务菜单</span>
        <i class="el-icon-close tab-close"></i>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-filter-section">
      <div class="search-controls">
        <!-- 搜索框 -->
        <div class="search-box">
          <el-input
            v-model="searchKeyword"
            placeholder="请输入关键字"
            prefix-icon="el-icon-search"
            class="search-input"
            clearable
            @keyup.enter.native="handleSearch"
            @clear="handleSearch"
          />
        </div>

        <!-- 机组类型下拉选择 -->
        <div class="filter-select">
          <el-select
            v-model="selectedType"
            placeholder="机组类型"
            class="type-select"
            clearable
            @change="handleTypeChange"
          >
            <el-option label="全部" value=""></el-option>
            <el-option label="调峰机组" value="调峰机组"></el-option>
            <el-option label="基荷机组" value="基荷机组"></el-option>
            <el-option label="调频机组" value="调频机组"></el-option>
          </el-select>
        </div>
      </div>

      <!-- 新增按钮 -->
      <el-button
        type="primary"
        class="add-button"
        @click="handleAdd"
      >
        新增
      </el-button>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table
        :data="currentPageData"
        style="width: 100%"
        :height="tableHeight"
        class="data-table"
        border
        stripe
        v-loading="loading"
        element-loading-text="加载中..."
      >
        <!-- 序号列 -->
        <el-table-column
          type="index"
          label="序号"
          width="80"
          align="center"
          :index="getIndex"
        />

        <!-- 机组名称列 -->
        <el-table-column
          prop="unitName"
          label="机组名称"
          min-width="200"
          show-overflow-tooltip
        />

        <!-- 机组类型列 -->
        <el-table-column
          prop="unitType"
          label="机组类型"
          width="120"
        />

        <!-- 聚合资源数量列 -->
        <el-table-column
          prop="resourceCount"
          label="聚合资源数量"
          width="140"
          align="center"
        />

        <!-- 装机容量列 -->
        <el-table-column
          prop="installedCapacity"
          label="装机容量(MW)"
          width="140"
          align="center"
        />

        <!-- 可调节容量列 -->
        <el-table-column
          prop="adjustableCapacity"
          label="可调节容量(MW)"
          width="140"
          align="center"
        />

        <!-- 运行状态列 -->
        <el-table-column
          prop="status"
          label="运行状态"
          width="100"
          align="center"
        >
          <template slot-scope="scope">
            <el-tag
              :type="getStatusType(scope.row.status)"
              size="small"
            >
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 操作列 -->
        <el-table-column
          label="操作"
          width="160"
          align="center"
          fixed="right"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="handleEdit(scope.row)"
              class="action-btn"
            >
              编辑
            </el-button>
            <el-button
              type="text"
              size="small"
              @click="handleDelete(scope.row)"
              class="action-btn delete-btn"
            >
              删除
            </el-button>
            <el-button
              type="text"
              size="small"
              @click="handleView(scope.row)"
              class="action-btn"
            >
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <div class="pagination-wrapper">
        <div class="pagination-info">
          共 {{ total }} 条记录，第 {{ currentPage }} / {{ Math.ceil(total / pageSize) || 1 }} 页
        </div>
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="prev, pager, next"
          :total="total"
          class="pagination"
          :pager-count="5"
          small
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "AggregationList",
  data() {
    return {
      // 搜索筛选相关
      searchKeyword: "",
      selectedType: "",

      // 表格相关
      tableData: [],
      loading: false,
      tableHeight: 600,

      // 分页相关
      currentPage: 1,
      pageSize: 15
    };
  },
  computed: {
    // 过滤后的数据
    filteredData() {
      let data = [...this.tableData];

      // 关键字搜索
      if (this.searchKeyword) {
        data = data.filter(item =>
          item.unitName.toLowerCase().includes(this.searchKeyword.toLowerCase())
        );
      }

      // 类型筛选
      if (this.selectedType) {
        data = data.filter(item => item.unitType === this.selectedType);
      }

      return data;
    },

    // 总数
    total() {
      return this.filteredData.length;
    },

    // 当前页数据
    currentPageData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.filteredData.slice(start, end);
    }
  },
  mounted() {
    this.initData();
    this.calculateTableHeight();
    window.addEventListener('resize', this.calculateTableHeight);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.calculateTableHeight);
  },
  methods: {
    // 初始化数据
    initData() {
      this.loading = true;
      // 模拟数据 - 根据Figma设计生成
      setTimeout(() => {
        this.tableData = Array.from({ length: 15 }, (_, index) => ({
          id: index + 1,
          unitName: '1#调峰机组',
          unitType: '调峰机组',
          resourceCount: Math.floor(Math.random() * 50) + 20,
          installedCapacity: (Math.random() * 200 + 100).toFixed(1),
          adjustableCapacity: (Math.random() * 100 + 50).toFixed(1),
          status: index % 3 === 0 ? '正常' : index % 3 === 1 ? '离线' : '故障'
        }));
        this.loading = false;
      }, 500);
    },

    // 计算表格高度
    calculateTableHeight() {
      const windowHeight = window.innerHeight;
      const headerHeight = 80; // 顶部导航高度
      const tabHeight = 40; // 标签页高度
      const searchHeight = 80; // 搜索区域高度
      const paginationHeight = 60; // 分页器高度
      const padding = 40; // 内边距

      this.tableHeight = windowHeight - headerHeight - tabHeight - searchHeight - paginationHeight - padding;

      // 确保最小高度
      if (this.tableHeight < 300) {
        this.tableHeight = 300;
      }
    },

    // 获取序号
    getIndex(index) {
      return (this.currentPage - 1) * this.pageSize + index + 1;
    },

    // 获取状态标签类型
    getStatusType(status) {
      switch (status) {
        case '正常':
          return 'success';
        case '离线':
          return 'warning';
        case '故障':
          return 'danger';
        default:
          return 'info';
      }
    },

    // 搜索处理
    handleSearch() {
      this.currentPage = 1;
    },

    // 类型变更处理
    handleTypeChange() {
      this.currentPage = 1;
    },

    // 新增处理
    handleAdd() {
      this.$message.success('新增功能待开发');
    },

    // 编辑处理
    handleEdit(row) {
      this.$message.info(`编辑机组: ${row.unitName}`);
    },

    // 删除处理
    handleDelete(row) {
      this.$confirm(`确定要删除机组 "${row.unitName}" 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 这里应该调用删除API
        this.$message.success('删除成功');
        this.initData(); // 重新加载数据
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },

    // 查看处理
    handleView(row) {
      this.$message.info(`查看机组详情: ${row.unitName}`);
    },

    // 分页变更处理
    handleCurrentChange(page) {
      this.currentPage = page;
    }
  }
};
</script>

<style scoped lang="scss">
.aggregation-page {
  width: 100%;
  height: 100vh;
  background-color: var(--BG);
  padding: 0;
  overflow: hidden;
}

/* 标签页导航样式 */
.tab-navigation {
  display: flex;
  align-items: center;
  background-color: var(--BG1);
  border-bottom: 1px solid var(--BG3);
  padding: 0 24px;
  height: 40px;
  gap: 4px;

  .tab-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 4px 4px 0 0;
    cursor: pointer;
    font-size: 12px;
    line-height: 20px;
    min-width: 100px;
    height: 24px;

    &.active {
      background-color: var(--BG);
      color: var(--T1);
      border: 1px solid var(--BG3);
      border-bottom: none;
    }

    &:not(.active) {
      background-color: #DCDFE6;
      color: var(--T1);
    }

    .tab-text {
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .tab-close {
      font-size: 8px;
      color: var(--T2);
      cursor: pointer;

      &:hover {
        color: var(--T1);
      }
    }
  }
}

/* 搜索筛选区域样式 */
.search-filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--BG1);
  padding: 24px;
  border-radius: 4px;
  margin: 20px;
  margin-bottom: 0;

  .search-controls {
    display: flex;
    gap: 16px;
    align-items: center;
  }

  .search-box {
    position: relative;

    .search-input {
      width: 240px;

      ::v-deep .el-input__inner {
        height: 32px;
        line-height: 32px;
        border-radius: 16px;
        border: 1px solid #F0F2F5;
        background-color: #FFFFFF;
        color: #13171F;
        font-size: 14px;
        padding-left: 32px;

        &::placeholder {
          color: #C9CDD4;
          font-size: 14px;
        }

        &:focus {
          border-color: #C9CDD4;
          box-shadow: 0px 0px 0px 0px rgba(240, 240, 240, 1);
        }
      }

      ::v-deep .el-input__prefix {
        left: 8px;
        color: #C9CDD4;
      }
    }
  }

  .filter-select {
    .type-select {
      width: 240px;

      ::v-deep .el-input__inner {
        height: 32px;
        line-height: 32px;
        border-radius: 4px;
        border: 1px solid #C9CDD4;
        background-color: #FFFFFF;
        color: #13171F;
        font-size: 14px;
        padding: 0 16px;

        &::placeholder {
          color: #9CA3AF;
          font-size: 14px;
        }
      }

      ::v-deep .el-input__suffix {
        right: 16px;
      }
    }
  }

  .add-button {
    background-color: var(--ZS);
    border-color: var(--ZS);
    color: #FFFFFF;
    font-size: 14px;
    font-weight: 400;
    padding: 5px 16px;
    border-radius: 3px;
    height: 32px;

    &:hover {
      background-color: var(--ZS-hover);
      border-color: var(--ZS-hover);
    }

    &:active {
      background-color: var(--ZS-active);
      border-color: var(--ZS-active);
    }
  }
}

/* 表格区域样式 */
.table-section {
  background-color: var(--BG1);
  margin: 20px;
  border-radius: 4px;
  padding: 24px;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .data-table {
    flex: 1;

    ::v-deep .el-table__header-wrapper {
      .el-table__header {
        th {
          background-color: #FAFCFF;
          border-bottom: 1px solid #DCDFE6;
          color: var(--T1);
          font-size: 12px;
          font-weight: 400;
          padding: 10px 24px;
          height: 40px;
        }
      }
    }

    ::v-deep .el-table__body-wrapper {
      .el-table__body {
        tr {
          &:hover {
            background-color: var(--BG);
          }

          td {
            border-bottom: 1px solid #DCDFE6;
            color: var(--T1);
            font-size: 12px;
            padding: 10px 24px;
            height: 40px;
          }
        }
      }
    }

    ::v-deep .el-table__border-left-patch {
      border-right: 1px solid #DCDFE6;
    }

    ::v-deep .el-table__border-right-patch {
      border-left: 1px solid #DCDFE6;
    }
  }

  .action-btn {
    color: var(--ZS);
    font-size: 14px;
    padding: 6px 8px;
    margin: 0 4px;

    &:hover {
      color: var(--ZS-hover);
    }

    &.delete-btn {
      color: #F53F3F;

      &:hover {
        color: #FF7875;
      }
    }
  }
}

/* 分页器样式 */
.pagination-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--BG3);

  .pagination-info {
    color: var(--T2);
    font-size: 14px;
  }

  .pagination {
    ::v-deep .el-pager {
      li {
        background-color: transparent;
        color: var(--T2);
        font-size: 14px;
        min-width: 32px;
        height: 32px;
        line-height: 32px;
        border-radius: 4px;
        margin: 0 2px;

        &.active {
          background-color: var(--ZS);
          color: #FFFFFF;
        }

        &:hover:not(.active) {
          background-color: var(--BG);
          color: var(--T1);
        }
      }
    }

    ::v-deep .btn-prev,
    ::v-deep .btn-next {
      background-color: transparent;
      color: var(--T2);
      font-size: 14px;
      width: 32px;
      height: 32px;
      line-height: 32px;
      border-radius: 4px;
      margin: 0 2px;

      &:hover {
        background-color: var(--BG);
        color: var(--T1);
      }

      &:disabled {
        color: var(--T3);
        cursor: not-allowed;
      }
    }
  }
}

/* 响应式适配 */
@media (max-width: 1200px) {
  .search-filter-section {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;

    .search-controls {
      justify-content: center;
    }

    .add-button {
      align-self: center;
    }
  }
}

@media (max-width: 768px) {
  .aggregation-page {
    padding: 10px;
  }

  .search-filter-section,
  .table-section {
    margin: 10px 0;
    padding: 16px;
  }

  .search-controls {
    flex-direction: column;
    gap: 12px;

    .search-box .search-input,
    .filter-select .type-select {
      width: 100%;
    }
  }

  .data-table {
    ::v-deep .el-table__header-wrapper,
    ::v-deep .el-table__body-wrapper {
      font-size: 12px;
    }
  }

  .pagination-wrapper {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }
}
</style>